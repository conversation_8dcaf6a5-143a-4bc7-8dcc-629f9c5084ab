# 💊 Prescription OCR - Trích xuất Text từ Đơn Thuốc

Ứng dụng Python sử dụng OCR (Optical Character Recognition) để trích xuất text từ ảnh đơn thuốc, hỗ trợ cả tiếng Việt và tiếng Anh.

## ✨ Tính năng

- 🔍 **OCR đa phương pháp**: Sử dụng cả Tesseract và EasyOCR để đảm bảo độ chính xác cao
- 🖼️ **Xử lý ảnh thông minh**: Tự động tiền xử lý ảnh để tối ưu cho OCR
- 💊 **Trích xuất thông tin đơn thuốc**: Tự động nhận diện tên bệnh nhân, bá<PERSON> s<PERSON>, thuốc, ngày tháng
- 🌐 **API RESTful**: FastAPI backend với documentation tự động
- 🎨 **Giao diện thân thiện**: Streamlit UI dễ sử dụng
- 🇻🇳 **Hỗ trợ tiếng Việt**: Tối ưu cho đơn thuốc tiếng Việt

## 🚀 Cài đặt

### Yêu cầu hệ thống

- Python 3.8+
- Tesseract OCR
- Windows/Linux/macOS

### 1. Cài đặt Tesseract OCR

#### Windows:
```bash
# Tải và cài đặt từ: https://github.com/UB-Mannheim/tesseract/wiki
# Hoặc sử dụng chocolatey:
choco install tesseract
```

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install tesseract-ocr tesseract-ocr-vie
```

#### macOS:
```bash
brew install tesseract tesseract-lang
```

### 2. Clone repository và cài đặt dependencies

```bash
git clone <repository-url>
cd prescription-ocr
pip install -r requirements.txt
```

## 🏃‍♂️ Chạy ứng dụng

### Chạy API Server

```bash
# Cách 1: Sử dụng script
python run_api.py

# Cách 2: Chạy trực tiếp
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

API sẽ chạy tại: http://localhost:8000

### Chạy Streamlit UI

```bash
# Cách 1: Sử dụng script
python run_ui.py

# Cách 2: Chạy trực tiếp
streamlit run ui/streamlit_app.py --server.port 8501
```

UI sẽ chạy tại: http://localhost:8501

## 📖 Sử dụng

### 1. Sử dụng qua Web UI

1. Mở trình duyệt và truy cập http://localhost:8501
2. Upload ảnh đơn thuốc (JPG, PNG, JPEG)
3. Nhấn "Trích xuất Text"
4. Xem kết quả trong các tab:
   - **Thông tin đơn thuốc**: Thông tin được trích xuất tự động
   - **Tesseract OCR**: Kết quả từ Tesseract
   - **EasyOCR**: Kết quả từ EasyOCR

### 2. Sử dụng qua API

#### Upload và trích xuất text:

```bash
curl -X POST "http://localhost:8000/extract-text" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@path/to/prescription.jpg"
```

#### Kiểm tra trạng thái:

```bash
curl -X GET "http://localhost:8000/health"
```

### 3. Sử dụng trong Python

```python
import requests

# Upload ảnh
with open('prescription.jpg', 'rb') as f:
    files = {'file': f}
    response = requests.post('http://localhost:8000/extract-text', files=files)
    result = response.json()

print("Tesseract result:", result['tesseract_result'])
print("EasyOCR result:", result['easyocr_result'])
print("Prescription info:", result['prescription_info'])
```

## 📁 Cấu trúc Project

```
prescription-ocr/
├── app/                          # FastAPI backend
│   ├── __init__.py
│   ├── main.py                   # API endpoints
│   ├── ocr_utils.py             # OCR processing logic
│   └── image_preprocessing.py    # Image processing utilities
├── ui/                          # Streamlit frontend
│   ├── __init__.py
│   └── streamlit_app.py         # Web UI
├── requirements.txt             # Python dependencies
├── .env                        # Environment variables
├── run_api.py                  # API server launcher
├── run_ui.py                   # UI launcher
└── README.md                   # Documentation
```

## ⚙️ Cấu hình

Chỉnh sửa file `.env` để tùy chỉnh:

```env
# API Settings
API_HOST=0.0.0.0
API_PORT=8000
STREAMLIT_PORT=8501

# OCR Settings
TESSERACT_CONFIG=--oem 3 --psm 6 -l vie+eng
EASYOCR_LANGUAGES=vi,en
OCR_CONFIDENCE_THRESHOLD=0.5
```

## 🔧 API Documentation

Khi API server đang chạy, truy cập:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### Endpoints

- `POST /extract-text`: Upload ảnh và trích xuất text
- `GET /health`: Kiểm tra trạng thái hệ thống
- `GET /`: Root endpoint

## 🎯 Tối ưu hóa cho Đơn Thuốc

Ứng dụng được tối ưu đặc biệt cho đơn thuốc Việt Nam:

- **Tiền xử lý ảnh**: Tăng contrast, giảm noise, sửa độ nghiêng
- **Nhận diện thông tin**: Tự động trích xuất tên bệnh nhân, bác sĩ, thuốc
- **Đa phương pháp OCR**: Kết hợp Tesseract và EasyOCR
- **Xử lý đa phiên bản**: Thử nhiều cách xử lý ảnh khác nhau

## 🐛 Troubleshooting

### Lỗi Tesseract không tìm thấy:
```bash
# Windows: Thêm Tesseract vào PATH
# Hoặc set biến môi trường:
set TESSDATA_PREFIX=C:\Program Files\Tesseract-OCR\tessdata
```

### Lỗi EasyOCR:
```bash
# Cài đặt lại EasyOCR
pip uninstall easyocr
pip install easyocr
```

### Lỗi dependencies:
```bash
# Cài đặt lại tất cả dependencies
pip install -r requirements.txt --force-reinstall
```

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên hệ

- Email: <EMAIL>
- Project Link: [https://github.com/yourusername/prescription-ocr](https://github.com/yourusername/prescription-ocr)

## 🙏 Acknowledgments

- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract)
- [EasyOCR](https://github.com/JaidedAI/EasyOCR)
- [FastAPI](https://fastapi.tiangolo.com/)
- [Streamlit](https://streamlit.io/)
