import cv2
import numpy as np
from PIL import Image
import pytesseract
import easyocr
import re
from typing import List, Tuple, Dict
import logging
from .image_preprocessing import PrescriptionImageProcessor

logger = logging.getLogger(__name__)

class OCRProcessor:
    """
    Class xử lý OCR chuyên biệt cho đơn thuốc
    """

    def __init__(self):
        try:
            self.easyocr_reader = easyocr.Reader(['vi', 'en'])
            logger.info("EasyOCR reader initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
            self.easyocr_reader = None

        # Khởi tạo image processor
        self.image_processor = PrescriptionImageProcessor()

    def preprocess_prescription_image(self, image: np.ndarray) -> np.ndarray:
        """
        Tiền xử lý ảnh đơn thuốc để tối ưu OCR
        """
        # <PERSON><PERSON><PERSON><PERSON> sang grayscale nếu cần
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Tăng contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Giảm noise
        denoised = cv2.medianBlur(enhanced, 3)

        # Adaptive threshold để xử lý ánh sáng không đều
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        # Morphological operations để làm sạch
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        return cleaned

    def extract_text_tesseract(self, image: np.ndarray) -> Dict[str, any]:
        """
        Trích xuất text sử dụng Tesseract với cấu hình tối ưu cho đơn thuốc
        """
        try:
            # Cấu hình Tesseract
            config = '--oem 3 --psm 6 -l vie+eng'

            # Trích xuất text
            text = pytesseract.image_to_string(image, config=config)

            # Trích xuất thông tin chi tiết
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)

            # Lọc các từ có confidence cao
            words = []
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > 30:  # Confidence > 30%
                    word_info = {
                        'text': str(data['text'][i]),
                        'confidence': float(data['conf'][i]),
                        'bbox': (int(data['left'][i]), int(data['top'][i]),
                                int(data['width'][i]), int(data['height'][i]))
                    }
                    words.append(word_info)

            return {
                'text': text.strip(),
                'words': words,
                'method': 'tesseract'
            }

        except Exception as e:
            logger.error(f"Tesseract OCR error: {e}")
            return {'text': '', 'words': [], 'method': 'tesseract', 'error': str(e)}

    def extract_text_easyocr(self, image: np.ndarray) -> Dict[str, any]:
        """
        Trích xuất text sử dụng EasyOCR
        """
        try:
            if self.easyocr_reader is None:
                return {'text': '', 'words': [], 'method': 'easyocr', 'error': 'EasyOCR not available'}

            results = self.easyocr_reader.readtext(image)

            text_lines = []
            words = []

            for (bbox, text, confidence) in results:
                if confidence > 0.3:  # Confidence > 30%
                    text_lines.append(text)
                    word_info = {
                        'text': str(text),
                        'confidence': float(confidence * 100),  # Convert to percentage
                        'bbox': [[float(x), float(y)] for x, y in bbox]  # Convert numpy arrays to lists
                    }
                    words.append(word_info)

            return {
                'text': '\n'.join(text_lines),
                'words': words,
                'method': 'easyocr'
            }

        except Exception as e:
            logger.error(f"EasyOCR error: {e}")
            return {'text': '', 'words': [], 'method': 'easyocr', 'error': str(e)}

    def extract_prescription_info(self, text: str) -> Dict[str, any]:
        """
        Trích xuất thông tin cụ thể từ đơn thuốc
        """
        info = {
            'medicines': [],
            'doctor_name': '',
            'patient_name': '',
            'date': '',
            'hospital': '',
            'dosage_instructions': []
        }

        lines = text.split('\n')

        # Patterns để tìm thông tin
        medicine_patterns = [
            r'(?i)(thuốc|medicine|drug)\s*:?\s*(.+)',
            r'(?i)(tên thuốc|medication)\s*:?\s*(.+)',
        ]

        doctor_patterns = [
            r'(?i)(bác sĩ|doctor|dr\.?)\s*:?\s*(.+)',
            r'(?i)(bs\.?)\s*:?\s*(.+)',
        ]

        patient_patterns = [
            r'(?i)(bệnh nhân|patient|tên)\s*:?\s*(.+)',
            r'(?i)(họ tên|name)\s*:?\s*(.+)',
        ]

        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(?i)(ngày|date)\s*:?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
        ]

        # Tìm thông tin trong text
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Tìm thuốc
            for pattern in medicine_patterns:
                match = re.search(pattern, line)
                if match:
                    info['medicines'].append(match.group(2).strip())

            # Tìm bác sĩ
            for pattern in doctor_patterns:
                match = re.search(pattern, line)
                if match:
                    info['doctor_name'] = match.group(2).strip()

            # Tìm bệnh nhân
            for pattern in patient_patterns:
                match = re.search(pattern, line)
                if match:
                    info['patient_name'] = match.group(2).strip()

            # Tìm ngày
            for pattern in date_patterns:
                match = re.search(pattern, line)
                if match:
                    if len(match.groups()) > 1:
                        info['date'] = match.group(2).strip()
                    else:
                        info['date'] = match.group(1).strip()

        return info

    def process_prescription(self, image: np.ndarray) -> Dict[str, any]:
        """
        Xử lý hoàn chỉnh một ảnh đơn thuốc với nhiều phiên bản xử lý
        """
        # Tạo nhiều phiên bản xử lý của ảnh
        image_versions = self.image_processor.create_multiple_versions(image)

        best_tesseract_result = {'text': '', 'words': [], 'method': 'tesseract'}
        best_easyocr_result = {'text': '', 'words': [], 'method': 'easyocr'}

        # Thử OCR trên từng phiên bản và chọn kết quả tốt nhất
        for version_name, processed_image in image_versions:
            # Tesseract OCR
            tesseract_result = self.extract_text_tesseract(processed_image)
            if len(tesseract_result['text']) > len(best_tesseract_result['text']):
                best_tesseract_result = tesseract_result
                best_tesseract_result['version_used'] = version_name

            # EasyOCR (sử dụng ảnh gốc cho EasyOCR vì nó xử lý tốt ảnh màu)
            if version_name == 'original':
                easyocr_result = self.extract_text_easyocr(image)
                if len(easyocr_result['text']) > len(best_easyocr_result['text']):
                    best_easyocr_result = easyocr_result
                    best_easyocr_result['version_used'] = version_name

        # Kết hợp kết quả
        combined_text = best_tesseract_result['text'] + '\n' + best_easyocr_result['text']

        # Trích xuất thông tin đơn thuốc
        prescription_info = self.extract_prescription_info(combined_text)

        return {
            'tesseract_result': best_tesseract_result,
            'easyocr_result': best_easyocr_result,
            'prescription_info': prescription_info,
            'combined_text': combined_text,
            'processing_info': {
                'tesseract_version_used': best_tesseract_result.get('version_used', 'unknown'),
                'easyocr_version_used': best_easyocr_result.get('version_used', 'unknown'),
                'total_versions_tested': len(image_versions)
            }
        }
