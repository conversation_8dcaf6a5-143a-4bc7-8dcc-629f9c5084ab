from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import cv2
import numpy as np
from PIL import Image
import io
import os
from typing import Dict, Any
import logging
from .ocr_utils import OCRProcessor
from .simple_ocr import SimpleOCRProcessor
import pytesseract

# Cấu hình logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Tự động tìm và set đường dẫn Tesseract
def setup_tesseract():
    """Tự động tìm và cấu hình Tesseract"""
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe",
        "tesseract"  # Nếu đã có trong PATH
    ]

    for path in possible_paths:
        try:
            if path == "tesseract":
                # Test nếu đã có trong PATH
                pytesseract.get_tesseract_version()
                logger.info("Tesseract found in PATH")
                return True
            elif os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                pytesseract.get_tesseract_version()
                logger.info(f"Tesseract configured at: {path}")
                return True
        except:
            continue

    logger.warning("Tesseract not found - only EasyOCR will be available")
    return False

# Setup Tesseract
tesseract_available = setup_tesseract()

app = FastAPI(
    title="Prescription OCR API",
    description="API để trích xuất text từ ảnh đơn thuốc",
    version="1.0.0"
)

# Cấu hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Khởi tạo OCR processors
ocr_processor = OCRProcessor()
simple_ocr_processor = SimpleOCRProcessor()



@app.get("/")
async def root():
    return {"message": "Prescription OCR API is running"}

@app.post("/extract-text")
async def extract_text(file: UploadFile = File(...)) -> Dict[str, Any]:
    """
    Endpoint để upload ảnh và trích xuất text
    """
    try:
        # Kiểm tra định dạng file
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File phải là ảnh")

        # Đọc ảnh
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))

        # Chuyển đổi sang numpy array
        image_np = np.array(image)

        # Xử lý ảnh bằng OCRProcessor
        result = ocr_processor.process_prescription(image_np)

        return {
            "filename": file.filename,
            "tesseract_result": result['tesseract_result']['text'],
            "easyocr_result": result['easyocr_result']['text'],
            "prescription_info": result['prescription_info'],
            "detailed_results": {
                "tesseract": result['tesseract_result'],
                "easyocr": result['easyocr_result']
            },
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error processing image: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi xử lý ảnh: {str(e)}")

@app.post("/extract-text-simple")
async def extract_text_simple(file: UploadFile = File(...)) -> Dict[str, Any]:
    """
    Endpoint đơn giản để upload ảnh và trích xuất text (nhanh hơn)
    """
    try:
        # Kiểm tra định dạng file
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File phải là ảnh")

        # Đọc ảnh
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))

        # Chuyển đổi sang numpy array
        image_np = np.array(image)

        # Xử lý ảnh bằng SimpleOCRProcessor
        result = simple_ocr_processor.process_prescription_simple(image_np)

        return {
            "filename": file.filename,
            "tesseract_result": result['tesseract_result'],
            "easyocr_result": result['easyocr_result'],
            "prescription_info": result['prescription_info'],
            "processing_info": result['processing_info'],
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error processing image (simple): {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi xử lý ảnh: {str(e)}")

@app.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    return {
        "status": "healthy",
        "tesseract_available": tesseract_available,
        "easyocr_available": ocr_processor.easyocr_reader is not None
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
